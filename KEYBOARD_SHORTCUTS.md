# Keyboard Shortcuts for Passo Scraper

## Overview
The Passo scraper now includes keyboard shortcuts to gracefully stop the scraping process and save the current progress immediately.

## Available Shortcuts

### Text Commands
Type any of the following commands followed by Enter to stop scraping:
- `q` - Quit scraping
- `quit` - Quit scraping  
- `stop` - Stop scraping
- `s` - Stop scraping

### Keyboard Interrupt
- `Ctrl+C` - Force stop scraping (traditional keyboard interrupt)

## How It Works

1. **Automatic Monitoring**: When the scraper starts, it automatically begins monitoring for keyboard input in a separate thread.

2. **Graceful Shutdown**: When a stop signal is received:
   - The current event being processed is completed
   - All scraped data is saved to the CSV file
   - Progress is saved for potential resume
   - Browser and resources are cleaned up properly

3. **Real-time Feedback**: The scraper provides clear feedback when:
   - Stop signal is received
   - Data is being saved
   - Cleanup is completed

## Usage Example

```bash
python3 passo.py
```

When the scraper starts, you'll see:
```
🚀 PASSO SCRAPER STARTED
============================================================
📋 KEYBOARD SHORTCUTS:
   • Type 'q', 'quit', 'stop', or 's' + Enter to stop scraping
   • Press Ctrl+C to force stop
   • Current progress will be saved automatically
============================================================
```

To stop the scraper, simply type one of the commands and press Enter:
```
stop
```

The scraper will respond:
```
🛑 STOP SIGNAL RECEIVED!
Stopping scraping and saving current progress...
🛑 Stop signal received. Saving current progress and exiting...
💾 DATA SAVED SUCCESSFULLY!
   • New records scraped: 45
   • Total records in file: 1245
   • File location: ./data/passo.csv
🏁 Scraping stopped by user request.
✅ Scraper cleanup completed.
```

## Testing

You can test the keyboard shortcut functionality without running the full scraper:

```bash
python3 test_keyboard_shortcut.py
```

This will simulate the scraping process and allow you to test the stop functionality.

## Technical Details

- **Thread Safety**: The keyboard listener runs in a separate daemon thread
- **Non-blocking**: The main scraping loop continues normally while monitoring for stop signals
- **Graceful Cleanup**: All resources (WebDriver, threads) are properly cleaned up
- **Progress Preservation**: Current progress is saved and can be resumed later
- **Error Handling**: Robust error handling for various input scenarios

## Benefits

1. **User Control**: Stop scraping at any time without losing progress
2. **Data Safety**: All scraped data is saved before shutdown
3. **Resource Management**: Proper cleanup prevents resource leaks
4. **Resume Capability**: Progress is saved for potential resume
5. **User-Friendly**: Clear instructions and feedback
