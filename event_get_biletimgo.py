from bs4 import BeautifulSoup
from selenium import webdriver
from googletrans import Translator
import pandas as pd
import time
from selenium.webdriver.common.by import By
from lxml import etree

driver = webdriver.Edge()
driver.get("https://www.biletimgo.com/etkinlik-takvimi")
driver.execute_script("document.body.style.zoom='50%'")
time.sleep(5)
try:
        j = 0
        while j < 10:
            driver.execute_script("window.scrollTo(0,document.body.scrollHeight)")
            time.sleep(3)
            next = driver.find_element(By.XPATH, '//*[@id="tab1"]/ul/div/button')
            driver.execute_script("arguments[0].click();", next)
            j += 1
except: 
    pass

html = driver.page_source
soup = BeautifulSoup(html,"html.parser")
links = []
for link in soup.findAll('a',{"class": "get-ticket"}):
    link = (link.get('href'))
    link = "https://www.biletimgo.com/"+ link
    links.append(link)

driver.quit()

event_list = pd.DataFrame()

event_list.insert(0,"Etkinlik Adı","")
event_list.insert(1,"Kategori","")
event_list.insert(2,"Konum","")
event_list.insert(3,"Başlangıç","")
event_list.insert(4,"Bitiş","")
event_list.insert(5,"Organizatör","")

event_names = []
event_cats = []

for link in links:
    driver = webdriver.Edge()
    driver.get(link)
    html = driver.page_source
    soup = BeautifulSoup(html,"html.parser")
    name=[ifade.text for ifade in soup.find_all("h1", {"style" :"position:relative; text-shadow: 0px 0px 5px #fff;"} )]
    event_names.append(name)
    cat = [ifade.text for ifade in soup.find_all("span", {"class" :"yaziboyut"} )]
    event_cats.append(cat)
    driver.quit()

def reduce_spaces(text):
  return " ".join(text.split())

for event in range(0,len(event_cats)):
    for event_lenght in range(0,len(event_cats[event])):

        event_cats[event][event_lenght] = event_cats[event][event_lenght].replace("\n","")
        event_cats[event][event_lenght] = event_cats[event][event_lenght].replace("\t"," ")
        event_cats[event][event_lenght] = event_cats[event][event_lenght].replace("Başlangıç","")
        event_cats[event][event_lenght] = event_cats[event][event_lenght].replace("Bitiş","")
        event_cats[event][event_lenght] = event_cats[event][event_lenght].replace("Organizatör","")
        event_cats[event][event_lenght] = event_cats[event][event_lenght].replace("Kategori","")
        event_cats[event][event_lenght] = event_cats[event][event_lenght].strip()
        event_cats[event][4] = reduce_spaces(event_cats[event][4])

for event in range(0,len(event_names)):
    event_names[event][0] = event_names[event][0].replace("\n","")
    event_names[event][0] = event_names[event][0].replace("\t","")

for event in range(0,len(event_cats)):
    event_info = {"Etkinlik Adı": event_names[event][0],
                  "Kategori": event_cats[event][3],
                  "Konum": event_cats[event][4],
                  "Başlangıç": event_cats[event][0],
                  "Bitiş": event_cats[event][1],
                  "Organizatör": event_cats[event][2]}
    event_list = event_list.append(event_info, ignore_index=True)

event_list.to_excel("Events_Biletimgo.xlsx",index=True,merge_cells=False)