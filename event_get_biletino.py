from bs4 import BeautifulSoup
from selenium import webdriver
from googletrans import Translator
import pandas as pd
import time
from selenium.webdriver.common.by import By
from lxml import etree

categories = {"Pop Müzik" : 101,
              "Rap Müzik": 102,
              "Rock Müzik": 103,
              "Elektronik Müzik" : 104,
              "Klasik Müizk": 105,
              "Jazz Müzik": 106,
              "Parti": 107,
              "<PERSON><PERSON><PERSON>baş<PERSON>": 108,
              "Üniversite Partisi": 109,
              "Müzik Festivali": 201,
              "Tiyatro Festivali": 202,
              "Aile & Çocuk Festivali": 203,
              "Tiyatro": 300,
              "Sinema": 400,
              "Konferans": 501,
              "Seminer": 502,
              "Çalıştay": 503,
              "Profesyonel Eğitim": 601,
              "Kişisel Gelişim": 602,
              "Psikoloji": 603,
              "Atölye": 604,
              "Üniversite Kulüp": 605,
              "Fuar": 700,
              "Topluluk Buluşmaları": 801,
              "Spiritüel": 802,
              "<PERSON>bi": 803,
              "Beslenme & Sportif": 804,
              "Outdoor & Kamp": 805,
              "Ödül Törenleri": 806,
              "Yardım & Bağış Toplama": 807,
              "Yemek Tadım": 901,
              "İçki Tadım": 902,
              "Standup": 1001,
              "Spor": 1100,
              "Tiyatro": 1201,
              "Atölye": 1202,
              "Kurs": 1203,
              "Aktivite": 1204,
              "Konser": 1205
              }


event_list= pd.DataFrame()

event_list.insert(0,"Etkinlik Adı", "")
event_list.insert(1,"Kategori", "")
event_list.insert(2,"Alt Kategori", "")
event_list.insert(3,"Tarih", "")
event_list.insert(4,"Konum", "")

events_name = []
events_cat = []
events_sub_cats = []
events_dates = []
events_location= []


for value in categories.items():
    link = "https://biletino.com/tr/search/?category="+str(value[1])+"&start=0&count=9&date=all"
    # str(value[0]) = kategori adı
    driver = webdriver.Edge()
    driver.get(link)
    
    events_sub_cats.append(str(value[0]))
    if str(value[1])[0:3] == "100":
        event_cat = "Standup"
    elif str(value[1])[0:2] == "10":
        event_cat = "Müzik"
    elif str(value[1])[0:2] == "20":
        event_cat = "Festival"
    elif str(value[1])[0:2] == "30":
        event_cat = "Tiyatro"
    elif str(value[1])[0:2] == "40":
        event_cat = "Sinema"
    elif str(value[1])[0:2] == "50":
        event_cat = "İş Hayatı"
    elif str(value[1])[0:2] == "60":
        event_cat = "Eğitim"
    elif str(value[1])[0:2] == "70":
        event_cat = "Fuar"
    elif str(value[1])[0:2] == "80":
        event_cat = "Sosyal Etkinlik"
    elif str(value[1])[0:2] == "90":
        event_cat = "Yeme & İçme"
    elif str(value[1])[0:2] == "11":
        event_cat = "Spor"
    elif str(value[1])[0:2] == "12":
        event_cat = "Çocuk"

    events_cat.append(event_cat)
    
    html = driver.page_source
    soup = BeautifulSoup(html,"html.parser")
    event_name = [ifade.text for ifade in soup.find_all("h3",{"class": "title"})]
    event_date = [ifade.text for ifade in soup.find_all("p",{"class": "card-text date"})]
    event_location = [ifade.text for ifade in soup.find_all("p",{"class": "card-text location"})]

    events_name.append(event_name)
    events_dates.append(event_date)
    events_location.append(event_location)
    
    driver.quit()

event_list_names = []
event_list_dates = []
event_list_location = []

def list_names_gatherer():
    for i in range(0,len(events_name)):
        for j in range(0,len(events_name[i])):
            event_list_names.append(events_name[i][j])

def list_dates_gatherer():
    for i in range(0,len(events_dates)):
        for j in range(0,len(events_dates[i])):
            event_list_dates.append(events_dates[i][j])

def list_location_gatherer():
    for i in range(0,len(events_location)):
        for j in range(0,len(events_location[i])):
            event_list_location.append(events_location[i][j])

list_names_gatherer()
list_dates_gatherer()
list_location_gatherer()

new_event_list_dates = []
def clear_dates():
  for word in event_list_dates:
    if word.startswith('\n'):
      word = word.replace('\n', '')
    new_event_list_dates.append(word)
  return new_event_list_dates

new_event_list_location = []
def clear_location():
  for word in event_list_location:
    if word.startswith('\n'):
      word = word.replace('\n', '')
    new_event_list_location.append(word)
  return new_event_list_location

clear_dates()
clear_location()

events_cats = []

for i in range(0,len(events_name)):
    for x in range(0,len(events_name[i])):
        if i == 0 or i == 1 or i == 2 or i == 3 or i == 4 or i == 5 or i == 6 or i == 7 or i == 8 :
            events_cats.append("Müzik")
        elif i == 9  or i == 10  or i == 11:
            events_cats.append("Festival")
        elif i == 12:
            events_cats.append("Tiyatro")
        elif i == 13:
            events_cats.append("Sinema")
        elif i == 14  or i == 15  or i == 16:
            events_cats.append("İş Hayatı")
        elif i == 17 or i == 18 or i == 19 or i == 20 or i == 21:
            events_cats.append("Eğitim")
        elif i == 22:
            events_cats.append("Fuar")
        elif i == 23 or i == 24 or i == 25 or i == 26 or i == 27 or i == 28 or i == 29:
            events_cats.append("Sosyal Etkinlik")
        elif i == 30 or i == 31:
            events_cats.append("Yeme & İçme")
        elif i == 32:
            events_cats.append("Standup")
        elif i == 33:
            events_cats.append("Spor")
        elif i == 34 or i == 35 or i == 36:
            events_cats.append("Çocuk")
    

for i in range(0, len(event_list_names)):
    event_info = {"Etkinlik Adı": event_list_names[i], "Kategori": events_cats[i], "Alt Kategori": "", "Tarih": new_event_list_dates[i], "Konum": new_event_list_location[i]}
    event_list = event_list.append(event_info, ignore_index= True)

event_list= event_list.drop_duplicates(subset="Etkinlik Adı", keep="first")

event_list.to_excel("Events_Biletino.xlsx",index=True,merge_cells=False)