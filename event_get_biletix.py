from bs4 import BeautifulSoup
from selenium import webdriver
from googletrans import Translator
import pandas as pd
import time

cats = ['Alternatif',
        'Blues',
        'Dans - Elektronik',
        'Dünya Müzik',
        'Heavy Metal',
        'Caz',
        'Klasik',
        'Latin - Tango',
        'New Age',
        'Parti',
        'Pop',
        'Rap - HipHop',
        'Rock',
        'Türk Sanat - Halk Müziği',
        '<PERSON><PERSON><PERSON> Müzik',
        '<PERSON><PERSON> <PERSON> Dan<PERSON>',
        '<PERSON><PERSON><PERSON> <PERSON> Gösteri',
        'Stand-Up',
        '<PERSON>hne - T<PERSON><PERSON>',
        '<PERSON>hne - Sin<PERSON>',
        'Opera',
        'Sahne - Müzikli Gösteri',
        '<PERSON><PERSON><PERSON> - Sir<PERSON>',
        'Diğer Sahne Sanatları',
        'Basketbol',
        'Dövüş Sporları',
        'Futbol',
        'Motor Sporları',
        'E-Sport',
        'Voleybol',
        'Tenis',
        'Buz Hokeyi',
        'Diğer Spor',
        '<PERSON><PERSON> - Gösteri',
        '<PERSON><PERSON> - Sir<PERSON>',
        '<PERSON>le - Tiyatro',
        '<PERSON><PERSON> - Sinema',
        '<PERSON>le - Müzikli Gösteri',
        'Zoo',
        'Theme Park',
        'Attraction Center',
        'Diğer Aile Eğlencesi'
        'Müze',
        'Atölye',
        'Eğitim',
        'MEB Onaylı Eğitim',
        'Sergi',
        'Sosyal Sorumluluk',
        'Konferans',
        'Fuar',
        'Ürün Satışı']

cat_list= pd.DataFrame()

links = ["https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:alternatif$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:blues$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:dans_elektronik$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:dunya_muzik$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:heavy_metal$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:jazz$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:klasik$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:latin_tango$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:newage$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:party$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:pop$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:rap_hiphop$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:rock$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:turksanat_halkmuzik$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:other$MUSIC",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:bale_dans$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:gosteri$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:stand_up$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:tiyatro$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:sinema$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:opera$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:musical$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:sirk$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:other$ART",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:basketbol$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:dovus_spor$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:futbol$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:motorspor$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:esport$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:voleybol$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:tenis$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:ice_hokey$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:other$SPORT",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:gosteri$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:sirk$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:tiyatro$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:sinema$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:musical$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:zoo$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:themepark$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:attractioncenter$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:other$FAMILY",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:muze$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:atolye$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:egitim$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:mebonayliegitim$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:sergi$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:sosyal_sorumluluk$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:konferans$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:fuar$OTHER",
         "https://www.biletix.com/search/TURKIYE/tr#!subcat_sb:urunsatisi$OTHER"]

event_cat = []
event_names = []
event_places = []
event_citys = []
event_times = []

for i in range(0,len(links)):
    driver = webdriver.Edge()
    driver.get(links[i])
    time.sleep(5)
    html = driver.page_source
    soup = BeautifulSoup(html,"html.parser")
  
    event_name=[ifade.text for ifade in soup.find_all("a",{"class":"ln1 searchResultEventName"})]
    event_place=[ifade.text for ifade in soup.find_all("a",{"class":"ln1 searchResultPlace"})]
    event_city=[ifade.text for ifade in soup.find_all("span",{"class":"ln2 searchResultCity"})]
    event_time=[ifade.text for ifade in soup.find_all("div",{"class":"grid_3 alpha omega"})]
    try:
        event_names.append(event_name)
    except:
        AttributeError
    try:
        event_places.append(event_place)
    except:
        AttributeError
    try:
        event_citys.append(event_city)
    except:
        AttributeError
    try:
        event_times.append(event_time)
    except:
        AttributeError
        
    driver.quit()

for x in range(0,len(event_names)):
    for y in range(0,len(event_names[x])):
        try:
            if event_names[x][y] == "":
                event_names[x].pop(y)
        except:
            IndexError

for x in range(0,len(event_places)):
    for y in range(0,len(event_places[x])):
        try:
            if event_places[x][y] == "":
                event_places[x].pop(y)
        except:
            IndexError

for x in range(0,len(event_citys)):
    for y in range(0,len(event_citys[x])):
        try:
            if event_citys[x][y] == "":
                event_citys[x].pop(y)
        except:
            IndexError

for x in range(0,len(event_names)):
    for y in range(0,len(event_names[x])):
        try:
            event_cat.append(cats[x])
        except:
            IndexError

event_names_new = []
event_places_new = []
event_citys_new = []
event_times_new = []

for x in range(0,len(event_times)):
    for y in range(0,len(event_times[x])):
            event_times[x][y] = event_times[x][y].replace("\n","")
            

for x in range(0,len(event_times)):
    for y in range(0,len(event_times[x])):
        try:
            if event_times[x][y] == "":
                event_times[x].pop(y)
        except:
            IndexError

for x in range(0,len(event_names)):
    for y in range(0,len(event_names[x])):
        try:
            event_names_new.append(event_names[x][y])
        except:
            IndexError

for x in range(0,len(event_places)):
    for y in range(0,len(event_places[x])):
        try:
            event_places_new.append(event_places[x][y])
        except:
            IndexError

for x in range(0,len(event_citys)):
    for y in range(0,len(event_citys[x])):
        try:
            event_citys_new.append(event_citys[x][y])
        except:
            IndexError

for x in range(0,len(event_times)):
    for y in range(0,len(event_times[x])):
        if y%2 == 0:
            try:
                event_times_new.append(event_times[x][y])
            except:
                IndexError
        else:
            pass
            

cat_list.insert(0,"Etkinlik Adı","")
cat_list.insert(1,"Kategori","")
cat_list.insert(2,"Mekan", "")
cat_list.insert(3,"Şehir","")
cat_list.insert(4,"Tarih","")

for event in range(0,len(event_names_new)):
    try:
        event_info = {"Etkinlik Adı": event_names_new[event],
                  "Kategori": event_cat[event],
                  "Mekan": event_places_new[event],
                  "Şehir": event_citys_new[event],
                  "Tarih": event_times_new[event]}
    except:
        IndexError
    cat_list = cat_list.append(event_info, ignore_index=True)

cat_list.to_excel("Data/Events_Biletix_New.xlsx",index=True,merge_cells=False)
