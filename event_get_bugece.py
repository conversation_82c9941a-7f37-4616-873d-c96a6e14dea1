from bs4 import BeautifulSoup
from selenium import webdriver
from googletrans import Translator
import pandas as pd
import time

cat_list= pd.DataFrame()

driver = webdriver.Edge()
driver.get("https://bugece.co/tr/events")
driver.execute_script("document.body.style.zoom='50%'")
time.sleep(5)
i = 0
while i < 10:
    driver.execute_script("window.scrollTo(0,document.body.scrollHeight)")
    time.sleep(3)

    i += 1

html = driver.page_source
soup = BeautifulSoup(html,"html.parser")
categories=[ifade.text for ifade in soup.find_all("a",{"draggable": "false"})]
dates = [ifade.text for ifade in soup.find_all("time")]

categories_new=[]
for i in range(0,len(categories)):
    try:
        if categories[i] == "":
            pass
        elif categories[i] == 'SATIN AL':
            pass
        elif categories[i] == 'Etkinlik Bildir':
            pass
        elif categories[i] == 'DETAYLAR':
            pass
        else: categories_new.append(categories[i])
    except:
        IndexError

new_dates = []
for i in range(0,len(dates)):
    if i % 3 ==0:
        new_dates.append(dates[i])



cat_list.insert(0,"Etkinlik", "")
cat_list.insert(1,"Mekan", "")
cat_list.insert(2,"Tarih", "")

for i in range(0,len(categories)):
    try:
        if i % 2 == 0:
            new_i = {"Etkinlik": categories_new[i], "Mekan": categories_new[i+1], "Tarih": new_dates[i]}
            cat_list = cat_list.append(new_i, ignore_index=True)
        else:
            pass
    except: 
        IndexError

cat_list.to_excel("Data/Events_BuGece.xlsx",index=True,merge_cells=False)