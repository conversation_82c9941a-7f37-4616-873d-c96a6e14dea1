from bs4 import BeautifulSoup
from selenium import webdriver
from googletrans import Translator
import pandas as pd
import time
from lxml import etree
from selenium.webdriver.common.by import By

cats = ["<PERSON><PERSON>",
        "<PERSON>ğiti<PERSON>",
        "Performans",
        "<PERSON><PERSON><PERSON><PERSON>",
        "Sin<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "Festival",
        "Spor",
        "Fuar"]

links = ["https://mobilet.com/tr/search/?eventTypes=sergi",
        "https://mobilet.com/tr/search/?eventTypes=eğitim",
        "https://mobilet.com/tr/search/?eventTypes=performans",
        "https://mobilet.com/tr/search/?eventTypes=müzik",
        "https://mobilet.com/tr/search/?eventTypes=sinema",
        "https://mobilet.com/tr/search/?eventTypes=tiyatro",
        "https://mobilet.com/tr/search/?eventTypes=festival",
        "https://mobilet.com/tr/search/?eventTypes=spor",
        "https://mobilet.com/tr/search/?eventTypes=fuar"]

names = []
date_places = []

for link in links:
    driver = webdriver.Edge()
    driver.get(link)
    driver.execute_script("document.body.style.zoom='50%'")
    time.sleep(5)
    try:
        j = 0
        while j < 10:
            driver.execute_script("window.scrollTo(0,document.body.scrollHeight)")
            time.sleep(3)
            next = driver.find_element(By.XPATH, '//*[@id="__next"]/div/main/div/div/div[2]/div/button/div')
            driver.execute_script("arguments[0].click();", next)
            j += 1
    except: 
        pass
    html = driver.page_source
    soup = BeautifulSoup(html,"html.parser")
    name=[ifade.text for ifade in soup.find_all("h3", {"class" :"EventCardstyles__EventTitle-sc-bsxxjx-6 cLoZoM"} )]
    names.append(name)
    date_place = [ifade.text for ifade in soup.find_all("p",{"class": "EventCardstyles__EventInfoText-sc-bsxxjx-9 RgxKO"})]
    date_places.append(date_place)
    driver.quit()

dates = []
places = []

for cat in range(0,len(date_places)):
    try:
        for id in range(0,len(date_places[cat])):
            if id % 2 == 0:
                dates.append(date_places[cat][id])
            else:
                places.append(date_places[cat][id])
    except:
        IndexError

event_names = []
for name in range(0,len(names)):
    for id in range(0,len(names[name])):
        event_names.append(names[name][id])

events = []

for name in range(0,len(event_names)):
    event = event_names[name] + " - " + dates[name] + " - " + places[name]
    events.append(event)

event_list = pd.DataFrame()

events_sergi = []
events_egitim = []
events_performnas = []
events_müzik = []
events_sinema = []
events_tiyatro = []
events_festival = []
events_spor = []
events_fuar = []

for i in range(0,len(cats)):
    if i == 0:
        events_sergi.append(events[0:len(names[i])])
        events_sergi = events_sergi.pop(0)
    elif i == 1:
        events_egitim.append(events[len(names[i-1]):len(names[i-1])+len(names[i])])
        events_egitim = events_egitim.pop(0)
    elif i == 2:
        events_performnas.append(events[len(names[i-2])+len(names[i-1]):
                                            len(names[i-2])+len(names[i-1])+len(names[i])])
        events_performnas = events_performnas.pop(0)
    elif i == 3:
        events_müzik.append(events[len(names[i-3])+len(names[i-2])+len(names[i-1]):
                                       len(names[i-3])+len(names[i-2])+len(names[i-1])+len(names[i])])
        events_müzik = events_müzik.pop(0)
    elif i == 4:
        events_sinema.append(events[(len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1])):
                                        len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1])+len(names[i])])
        events_sinema = events_sinema.pop(0)
    elif i == 5:
        events_tiyatro.append(events[len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1]):
                                         len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1])+len(names[i])])
        events_tiyatro = events_tiyatro.pop(0)
    elif i == 6:
        events_festival.append(events[len(names[i-6])+len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1]):
                                         len(names[i-6])+len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1])+len(names[i])])
        events_festival = events_festival.pop(0)
    elif i == 7:
        events_spor.append(events[len(names[i-7])+len(names[i-6])+len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1]):
                                      len(names[i-7])+len(names[i-6])+len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1])+len(names[i])])
        events_spor = events_spor.pop(0)
    elif i == 8:
        events_fuar.append(events[len(names[i-8])+len(names[i-7])+len(names[i-6])+len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1]):
                                      len(names[i-8])+len(names[i-7])+len(names[i-6])+len(names[i-5])+len(names[i-4])+len(names[i-3])+len(names[i-2])+len(names[i-1])+len(names[i])])
        events_fuar = events_fuar.pop(0)
    else:
        pass
    
    
for i in range(0,len(cats)):
    event_list.insert(0,cats[i],"")
    for j in range(0,len(names[i])):
        event_info = {cats[i]: names[i][j]}

        event_list = event_list.append(event_info, ignore_index=True)


for i in range(0,len(names[1])):
      event_info = {cats[1]: names[1][i]}

    

      event_list = event_list.append(event_info, ignore_index=True)
    
   
    
        