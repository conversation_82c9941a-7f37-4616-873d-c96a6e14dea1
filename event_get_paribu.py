from bs4 import BeautifulSoup
from selenium import webdriver
from googletrans import Translator
import pandas as pd
import time
from lxml import etree

links = ["vizyondakiler","gelecek-filmler"]
movies = []
for link in links:
    driver = webdriver.Edge()
    driver.get("https://www.paribucineverse.com/{}".format(link))
    html = driver.page_source
    soup = BeautifulSoup(html,"html.parser")
    categories=[ifade.text for ifade in soup.find_all("h4")]
    movies.append(categories)
    driver.quit()

movies_name = []
for x in range(0,2):
    for movie in movies[x]:
        movies_name.append(movie)

movies_link = []
for movie in range(0,len(movies_name)):
    movies_name[movie] = movies_name[movie].lower()
    movies_name[movie] = movies_name[movie].translate({ord(":"): None})
    movies_name[movie] = movies_name[movie].translate({ord("?"): None})
    movies_name[movie] = movies_name[movie].translate({ord("'"): None})
    movies_name[movie] = movies_name[movie].translate({ord(" "): ord("-")})
    movies_name[movie] = movies_name[movie].translate({ord("ı"): ord("i")})
    movies_name[movie] = movies_name[movie].translate({ord("ü"): ord("u")})
    movies_name[movie] = movies_name[movie].translate({ord("ş"): ord("s")})
    movies_name[movie] = movies_name[movie].translate({ord("ğ"): ord("g")})
    movies_name[movie] = movies_name[movie].translate({ord("ç"): ord("c")})
    movies_name[movie] = movies_name[movie].translate({ord("ö"): ord("o")})
    movies_name[movie] = movies_name[movie]+"-filmi"
    movies_link.append("https://www.paribucineverse.com/"+movies_name[movie])

movies_name = []
for x in range(0,2):
    for movie in movies[x]:
        movies_name.append(movie)

movie_cats=[]
for link in movies_link:
    driver = webdriver.Edge()
    driver.get(link)
    time.sleep(3)
    html = driver.page_source
    soup = BeautifulSoup(html,"html.parser")
    dom = etree.HTML(str(soup))
    categories= dom.xpath("/html/body/section[3]/div/div/div[1]/div[3]/text()")
    movie_cats.append(categories)
    driver.quit()

movie_list = pd.DataFrame()
movie_list.insert(0,"Name","")
movie_list.insert(1,"Categories","")

for movie in range(0,len(movies_name)):
    movie_desc = {"Name": movies_name[movie], "Categories": movie_cats[movie]}
    movie_list = movie_list.append(movie_desc, ignore_index=True)

movie_list.to_excel("Data/Events_Paribu.xlsx",index=True,merge_cells=False)
    
    