from bs4 import BeautifulSoup
from selenium import webdriver
from googletrans import Translator
import pandas as pd
import time

cats = ['DİĞER MÜZİK',
 'POP-ROCK',
 'FESTİVAL',
 'HEAVY METAL',
 'DJ - PARTY',
 'CA<PERSON>',
 '<PERSON><PERSON><PERSON><PERSON>KA<PERSON>',
 'KLASİK MÜZİK',
 'İKSV CAZ',
 'KOMBİNE', 
 'SPOR TOTO SÜPER LİG', 
 'DİĞER FUTBOL',
 'TİYAT<PERSON>',
 'STAND-UP',
 'SAHNE - MÜZİKAL',
 'ÇOCUK',
 'MÜZİKAL TİYATRO',
 'İKSV TİYATRO',
 'DİĞER SAHNE',
 'GÖSTERİ',
 'BASKETBOL KOMBİNE', 
 'ERKE<PERSON> BASKETBOL', 
 '<PERSON>AD<PERSON> BASKETBOL', 
 '<PERSON><PERSON><PERSON><PERSON><PERSON> SPORLARI',
 'MÜZE', 
 'MÜZE-SHOW',
 'EĞİTİM-SEMİNER',
 '<PERSON><PERSON><PERSON> FİLM',
 'TUR - GEZİ',
 'W<PERSON><PERSON><PERSON><PERSON>',
 'ÜYELİK KARTI',
 'KONFERANS',
 '<PERSON>EDİY<PERSON> KART',
 'SERG<PERSON>']

cat_list= pd.DataFrame()

links = ["https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/pop-muzik-rock-muzik-konser-biletleri/8615/13617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/alternatif-muzik-alternatif-metal-alternatif-rock-alternatif-pop-konser-biletleri/8615/12617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/muzik-festival-biletleri/8615/14617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/heavy-metal-muzik-rock-muzik-konser-biletleri/8615/38617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/elektronik-muzik-konser-biletleri/8615/41617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/caz-muzik-konser-biletleri/8615/42617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/muzikal/8615/45617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/klasik-muzik-muzik-konser-biletleri/8615/56617",
         "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/caz-festivali-biletleri/8615/57617",
         "https://www.passo.com.tr/tr/kategori/futbol-mac-biletleri/futbol-sezon-biletleri-kombine-biletleri/4615/6617",
         "https://www.passo.com.tr/tr/kategori/futbol-mac-biletleri/super-lig-futbol-biletleri/4615/8617",
         "https://www.passo.com.tr/tr/kategori/futbol-mac-biletleri/futbol-biletleri/4615/9617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/tiyatro-biletleri/11615/18617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/stand-up/11615/44617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/muzikal/11615/45617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/cocuk-tiyatrosu/11615/46617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/muzikal-tiyatro/11615/48617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/tiyatro-festivali-tiyatro-biletleri-passo/11615/60617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/diger/11615/63617",
         "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/gosteri-biletleri/11615/77617",
         "https://www.passo.com.tr/tr/kategori/basketbol-mac-biletleri/turkiye-basketbol-lig-mac-kombine-biletleri/13615/26617",
         "https://www.passo.com.tr/tr/kategori/basketbol-mac-biletleri/erkek-basketbol-mac-biletleri/13615/29617",
         "https://www.passo.com.tr/tr/kategori/basketbol-mac-biletleri/kadin-basketbol-mac-biletleri/13615/30617",
         "https://www.passo.com.tr/tr/kategori/basketbol-mac-biletleri/dovus-sanatlari-dovus-sporlari-biletleri/13615/43617",
         "https://www.passo.com.tr/tr/kategori/muze-tarihi-mekan-saray-giris-biletleri/muze-biletleri/15615/20617",
         "https://www.passo.com.tr/tr/kategori/muze-tarihi-mekan-saray-giris-biletleri/muze-biletleri/15615/64617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/workshop-biletleri-egitim-seminer-biletleri/12615/17617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/online-film-biletleri/12615/55617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/travel-tur-gezi-doga-sporlari-biletler/12615/65617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/workshop/12615/70617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/uyelik-kart-satis/12615/73617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/konferans/12615/74617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/hediye-kart-passo/12615/76617",
         "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/sergi/12615/78617"]
def extract_events(links):
    try:
        for i in range(0,len(links)):
            driver = webdriver.Edge()
            driver.get(links[i])
            time.sleep(5)
            html = driver.page_source
            soup = BeautifulSoup(html,"html.parser")
            categories=[ifade.text for ifade in soup.find_all("div",{"class":"r-title"},limit=50)]
            cat_list.insert(i, cats[i], pd.Series(categories), False)
            driver.quit()

        cat_list.fillna("",inplace=True)

        cat_list.drop_duplicates()

        cat_list.to_excel("Data/Events_Passo.xlsx",index=True,merge_cells=False)
    except:
        pass

extract_events(links)