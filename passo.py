import os
import re
import time
import threading
from typing import Any, Dict, Optional, Tuple

import pandas as pd
from config import PassoConfig
from selenium import webdriver
from selenium.common.exceptions import TimeoutException, WebDriverException
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait


class KeyboardInterruptHandler:
    """<PERSON>les keyboard interrupts to stop scraping gracefully."""

    def __init__(self):
        self.stop_scraping = False
        self.input_thread = None
        self._setup_keyboard_listener()

    def _setup_keyboard_listener(self):
        """Setup keyboard listener in a separate thread."""
        self.input_thread = threading.Thread(target=self._listen_for_input, daemon=True)
        self.input_thread.start()

    def _listen_for_input(self):
        """Listen for keyboard input to stop scraping."""
        try:
            while not self.stop_scraping:
                try:
                    # Use input() with a timeout-like behavior
                    user_input = input().strip().lower()
                    if user_input in ['q', 'quit', 'stop', 's']:
                        print("\n" + "="*50)
                        print("🛑 STOP SIGNAL RECEIVED!")
                        print("Stopping scraping and saving current progress...")
                        print("="*50 + "\n")
                        self.stop_scraping = True
                        break
                except (EOFError, KeyboardInterrupt):
                    # Handle Ctrl+C or EOF
                    print("\n" + "="*50)
                    print("🛑 KEYBOARD INTERRUPT RECEIVED!")
                    print("Stopping scraping and saving current progress...")
                    print("="*50 + "\n")
                    self.stop_scraping = True
                    break
                except Exception:
                    # Ignore other input errors and continue listening
                    continue
        except Exception:
            pass

    def should_stop(self) -> bool:
        """Check if scraping should be stopped."""
        return self.stop_scraping

    def cleanup(self):
        """Clean up the keyboard handler."""
        self.stop_scraping = True
        if self.input_thread and self.input_thread.is_alive():
            # Give the thread a moment to finish
            self.input_thread.join(timeout=1.0)


class WebDriverManager:
    """Manages WebDriver initialization and lifecycle."""

    def __init__(self, wait_timeout: int = 10):
        self.wait_timeout = wait_timeout
        self.driver = None
        self.wait = None

    def initialize_driver(self):
        """Initialize or reinitialize the WebDriver."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass

        self.driver = webdriver.Firefox()
        self.wait = WebDriverWait(self.driver, self.wait_timeout)

    def quit_driver(self):
        """Safely quit the WebDriver."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass
            finally:
                self.driver = None
                self.wait = None


class ProgressManager:
    """Handles saving and loading scraping progress."""

    PROGRESS_FILE = "scraper_progress.txt"

    @staticmethod
    def save_progress(div_id: int, url: str):
        """Save current progress to a temporary file."""
        progress_data = {"last_processed_id": div_id, "current_url": url, "timestamp": time.time()}

        with open(ProgressManager.PROGRESS_FILE, "w") as f:
            f.write(f"{div_id}|{url}|{progress_data['timestamp']}")

    @staticmethod
    def load_progress() -> Tuple[Optional[int], Optional[str]]:
        """Load progress from temporary file."""
        if os.path.exists(ProgressManager.PROGRESS_FILE):
            try:
                with open(ProgressManager.PROGRESS_FILE, "r") as f:
                    content = f.read().strip()
                    if content:
                        parts = content.split("|")
                        if len(parts) >= 2:
                            return int(parts[0]), parts[1]
            except Exception:
                pass
        return None, None

    @staticmethod
    def clear_progress():
        """Remove the progress file."""
        if os.path.exists(ProgressManager.PROGRESS_FILE):
            try:
                os.remove(ProgressManager.PROGRESS_FILE)
            except Exception:
                pass


class EventDataExtractor:
    """Extracts event data from web pages."""

    def __init__(self, driver, wait):
        self.driver = driver
        self.wait = wait

    def get_thumbnail_url(self) -> Optional[str]:
        """Extract thumbnail URL from event page."""
        try:
            return self.driver.find_element(By.CSS_SELECTOR, "img.detail-img").get_attribute("src")
        except Exception:
            return None

    def get_event_name(self) -> Optional[str]:
        """Extract event name from event page."""
        try:
            return self.driver.find_element(By.CSS_SELECTOR, "div.font-weight-bold.ellipsis").text
        except Exception:
            return None

    def get_date(self) -> Optional[str]:
        """Extract event date from event page with multiple fallback options."""
        # First, try to get date from the primary location
        try:
            primary_date_element = self.driver.find_element(
                By.XPATH, "/html/body/app-root/app-layout/app-event/section/div/div/div/div/div[2]/div[1]/h4"
            )
            if primary_date_element.text.strip() == "Tarih":
                # If there's text in h4, get the date from the ul/li element
                try:
                    date_element = self.driver.find_element(
                        By.XPATH, "/html/body/app-root/app-layout/app-event/section/div/div/div/div/div[2]/div[1]/ul/li"
                    )
                    return date_element.text.strip()
                except Exception:
                    pass
        except Exception:
            pass

        # If primary location doesn't work, try to extract date from description using regex
        # Try multiple description locations
        description_xpaths = [
            "/html/body/app-root/app-layout/app-event/section/div/div/div/div/div[1]/div[5]/div[1]",
            "/html/body/app-root/app-layout/app-event/section/div/div/div/div/div[1]/div[2]/div/div/div[1]"
        ]

        # Comprehensive regex patterns for Turkish date formats
        date_patterns = [
            # DD.MM.YYYY format
            r'\b(\d{1,2})\.(\d{1,2})\.(\d{4})\b',
            # DD/MM/YYYY format
            r'\b(\d{1,2})/(\d{1,2})/(\d{4})\b',
            # DD-MM-YYYY format
            r'\b(\d{1,2})-(\d{1,2})-(\d{4})\b',
            # Turkish month names with day and year
            r'\b(\d{1,2})\s+(Ocak|Şubat|Mart|Nisan|Mayıs|Haziran|Temmuz|Ağustos|Eylül|Ekim|Kasım|Aralık)\s+(\d{4})\b',
            # English month names with day and year
            r'\b(\d{1,2})\s+(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{4})\b',
            # Month name first format (Turkish)
            r'\b(Ocak|Şubat|Mart|Nisan|Mayıs|Haziran|Temmuz|Ağustos|Eylül|Ekim|Kasım|Aralık)\s+(\d{1,2}),?\s+(\d{4})\b',
            # Month name first format (English)
            r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{1,2}),?\s+(\d{4})\b',
            # YYYY-MM-DD format
            r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b',
            # DD Month YYYY format (without comma)
            r'\b(\d{1,2})\s+(Ocak|Şubat|Mart|Nisan|Mayıs|Haziran|Temmuz|Ağustos|Eylül|Ekim|Kasım|Aralık|January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{4})\b',
            # DD Month format (without year) - Turkish
            r'\b(\d{1,2})\s+(Ocak|Şubat|Mart|Nisan|Mayıs|Haziran|Temmuz|Ağustos|Eylül|Ekim|Kasım|Aralık)(?=\s*[\|\-\.]|\s*$)',
            # DD Month format (without year) - English
            r'\b(\d{1,2})\s+(January|February|March|April|May|June|July|August|September|October|November|December)(?=\s*[\|\-\.]|\s*$)',
            # Month DD format (without year) - Turkish
            r'\b(Ocak|Şubat|Mart|Nisan|Mayıs|Haziran|Temmuz|Ağustos|Eylül|Ekim|Kasım|Aralık)\s+(\d{1,2})(?=\s*[\|\-\.]|\s*$)',
            # Month DD format (without year) - English
            r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{1,2})(?=\s*[\|\-\.]|\s*$)',
        ]

        for xpath in description_xpaths:
            try:
                description_element = self.driver.find_element(By.XPATH, xpath)
                description_text = description_element.text

                for pattern in date_patterns:
                    match = re.search(pattern, description_text, re.IGNORECASE)
                    if match:
                        return match.group(0)

            except Exception:
                continue

        # Fallback to original selectors if all else fails
        date_selectors = [
            "div.main-slider-item div.text-white",
            "div.main-slider-item div.text-uppercase",
            "div.col-md-4.ticket-info ul li",
        ]

        for selector in date_selectors:
            try:
                date_text = self.driver.find_element(By.CSS_SELECTOR, selector).text
                if date_text:
                    return date_text
            except Exception:
                continue

        return None

    def get_location_name(self) -> Optional[str]:
        """Extract location name from event page with fallback options."""
        location_selectors = ["div.box.cursor-pointer h4 div", "div.box h4 div.text-primary.my-2.cursor-pointer"]

        for selector in location_selectors:
            try:
                location_name = self.driver.find_element(By.CSS_SELECTOR, selector).text
                if location_name:
                    return location_name
            except Exception:
                continue
        return None

    def get_coordinates(self) -> Optional[str]:
        """Extract coordinates from Google Maps link."""
        try:
            href = self.driver.find_element(By.CSS_SELECTOR, "div.box.cursor-pointer h4 a").get_attribute("href")
            match = re.search(r"q=(\d{1,2}\.?\d*,\d{1,2}\.?\d*)", href)
            return match.group(1) if match else None
        except Exception:
            return None

    def get_description(self) -> Optional[str]:
        """Extract and clean event description."""
        try:
            event_description_wrapper = self.driver.find_element(By.CSS_SELECTOR, "div.event-description-wrapper").text
            description = re.sub(r"\<.*?\>", "\n", event_description_wrapper).strip()
            return re.sub(r"\n+", "\n", description)
        except Exception:
            return None

    def get_location_url_and_address(self) -> Tuple[Optional[str], Optional[str]]:
        """Extract location URL and open address by navigating to location page."""
        location_selectors = ["div.box.cursor-pointer h4 div", "div.box h4 div.text-primary.my-2.cursor-pointer"]

        location_element = None
        for selector in location_selectors:
            try:
                location_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except Exception:
                continue

        if location_element is None:
            return None, None

        try:
            self.driver.execute_script("arguments[0].click();", location_element)

            # Wait for the new tab to open and switch to it
            self.wait.until(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, "h1.text-red.font-weight-bold.font-size-1875.mt-5.mb-3")
                )
            )
            print("Current Url: ", self.driver.current_url)
            time.sleep(0.75)

            location_url = self.driver.current_url
            try:
                open_address = self.driver.find_element(By.CSS_SELECTOR, "a.address").text
            except Exception:
                open_address = None

            return location_url, open_address
        except Exception as e:
            print(f"\n{'#' * 40}\nError: {e}\n{'#' * 40}\n")
            return None, None

    def extract_all_event_data(self) -> Dict[str, Any]:
        """Extract all event data from the current page."""
        # Wait for the event page to load
        self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.page-breadcrumb")))
        print("Current Url: ", self.driver.current_url)
        time.sleep(0.75)

        # Scrape data from the page
        page_data = {
            "website_url": self.driver.current_url,
            "thumbnail_url": self.get_thumbnail_url(),
            "event_name": self.get_event_name(),
            "date": self.get_date(),
            "location_name": self.get_location_name(),
            "coordinates": self.get_coordinates(),
            "description": self.get_description(),
        }

        location_url, open_address = self.get_location_url_and_address()
        page_data["location_url"] = location_url
        page_data["open_address"] = open_address

        return page_data


class NavigationHandler:
    """Handles navigation, session recovery, and browser tab management."""

    def __init__(self, driver, wait):
        self.driver = driver
        self.wait = wait

    def load_more_events(self) -> bool:
        """Try to click 'Load More' button to get additional events."""
        try:
            button = self.driver.find_element(By.XPATH, "//button[text()=' Daha Fazla Göster']")
            print("Trying to click 'Daha Fazla Göster' button.")
            self.driver.execute_script("arguments[0].click();", button)
            print("Clicked 'Daha Fazla Göster' button.")
            time.sleep(1.5)
            return True
        except Exception:
            return False

    def switch_back_to_main_page(self):
        """Close all tabs except the main one and switch back to it."""
        print("Switching back to the main page.")
        try:
            handles = self.driver.window_handles
            for handle in handles[1:]:
                self.driver.switch_to.window(handle)
                self.driver.close()
                time.sleep(0.75)
            self.driver.switch_to.window(handles[0])
            print("Current Url: ", self.driver.current_url)
        except Exception as e:
            print(f"\n{'#' * 40}\nError: {e}\n{'#' * 40}\n")

    def navigate_to_last_position(self, target_url: str, last_id: int) -> bool:
        """Navigate back to the last processed position."""
        try:
            print(f"Attempting to recover session. Going to: {target_url}")
            self.driver.get(target_url)

            # Wait for the main div element to be present
            main_div = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
            )
            time.sleep(1)

            # Load items until we find our last processed ID
            old_length = 0
            max_attempts = 50  # Prevent infinite loop
            attempts = 0

            while attempts < max_attempts:
                child_items = main_div.find_elements(By.XPATH, "./div[@id]")

                # Check if we found our target ID
                for item in child_items:
                    div_id = int(item.get_attribute("id"))
                    if div_id == last_id:
                        print(f"Successfully recovered to position: ID {last_id}")
                        return True

                # Try to load more items
                if not self.load_more_events():
                    print("No more items to load during recovery.")
                    break

                # Check if new items were loaded
                new_length = len(child_items)
                if new_length == old_length:
                    attempts += 1
                else:
                    attempts = 0  # Reset if we're making progress

                old_length = new_length
                time.sleep(1)

            print(f"Could not find last processed ID {last_id}. Starting from current position.")
            return False

        except Exception as e:
            print(f"\n{'#' * 40}\nError: {e}\n{'#' * 40}\n")
            return False

    def handle_session_error(self, web_driver_manager, target_url: str, last_id: int = None) -> bool:
        """Handle session errors by reinitializing driver and recovering position."""
        print("Session error detected. Attempting recovery...")

        try:
            web_driver_manager.initialize_driver()
            self.driver = web_driver_manager.driver
            self.wait = web_driver_manager.wait

            if last_id:
                return self.navigate_to_last_position(target_url, last_id)
            else:
                # Just navigate to the main page
                self.driver.get(target_url)
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items")))
                time.sleep(1)
                return True

        except Exception as e:
            print(f"\n{'#' * 40}\nError: {e}\n{'#' * 40}\n")
            return False


class PassoScraper:
    """Web scraper for Passo.com.tr events with session recovery."""

    def __init__(self):
        self.config = PassoConfig()
        self.existing_data = self._load_existing_data()
        self.existing_ids = self.existing_data["div_id"].values.tolist()

        # Initialize components
        self.web_driver_manager = WebDriverManager(self.config.DEFAULT_WAIT_TIMEOUT)
        self.data_extractor = None
        self.navigation_handler = None
        self.keyboard_handler = KeyboardInterruptHandler()

        # Print instructions for user
        print("\n" + "="*60)
        print("🚀 PASSO SCRAPER STARTED")
        print("="*60)
        print("📋 KEYBOARD SHORTCUTS:")
        print("   • Type 'q', 'quit', 'stop', or 's' + Enter to stop scraping")
        print("   • Press Ctrl+C to force stop")
        print("   • Current progress will be saved automatically")
        print("="*60 + "\n")

    def _load_existing_data(self) -> pd.DataFrame:
        """Load existing scraped data if available."""
        if os.path.exists(self.config.OUTPUT_FILE):
            return pd.read_csv(self.config.OUTPUT_FILE, sep=";", encoding="utf-8-sig")
        return pd.DataFrame(columns=self.config.DATA_COLUMNS)

    def _initialize_components(self):
        """Initialize data extractor and navigation handler with current driver."""
        if self.web_driver_manager.driver and self.web_driver_manager.wait:
            self.data_extractor = EventDataExtractor(self.web_driver_manager.driver, self.web_driver_manager.wait)
            self.navigation_handler = NavigationHandler(self.web_driver_manager.driver, self.web_driver_manager.wait)

    def _setup_initial_navigation(self, link: str, last_processed_id: Optional[int], retry_count: int) -> bool:
        """Setup initial navigation to the target URL with optional recovery."""
        try:
            # If we have previous progress and it's the same URL, try to recover
            if last_processed_id and retry_count == 0:
                if not self.navigation_handler.navigate_to_last_position(link, last_processed_id):
                    # If recovery fails, start fresh
                    self.web_driver_manager.driver.get(link)
            else:
                self.web_driver_manager.driver.get(link)

            # Wait for the main div element to be present
            self.web_driver_manager.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
            )
            time.sleep(0.75)
            return True
        except Exception as e:
            print(f"\n{'#' * 40}\nError in initial navigation: {e}\n{'#' * 40}\n")
            return False

    def _process_single_event(
        self, item, div_id: int, link: str, df: pd.DataFrame, last_processed_id: Optional[int], retry_count: int
    ) -> bool:
        """Process a single event item and extract its data."""
        print(f"\nEvent Item Id: {div_id}")

        # Skip if we're recovering and haven't reached our last position yet
        if last_processed_id and div_id <= last_processed_id and retry_count == 0:
            print(f"Skipping ID {div_id} - already processed or before last position")
            return True

        if div_id in self.existing_ids:
            print(f"ID already exists. Skipping.")
            return True

        try:
            # Click on the event item
            self.web_driver_manager.driver.execute_script("arguments[0].click();", item)

            # Wait for the new tab to open and switch to it
            self.web_driver_manager.wait.until(EC.new_window_is_opened)
            time.sleep(0.75)
            self.web_driver_manager.driver.switch_to.window(self.web_driver_manager.driver.window_handles[-1])

            if "etkinlik-grubu" not in self.web_driver_manager.driver.current_url:
                # Extract event data
                page_data = self.data_extractor.extract_all_event_data()
                page_data["div_id"] = div_id
                df.loc[len(df.index)] = page_data

                # Save progress
                ProgressManager.save_progress(div_id, link)

            # Close the current tab and switch back to the main tab
            self.navigation_handler.switch_back_to_main_page()
            time.sleep(0.75)
            return True

        except (WebDriverException, TimeoutException) as e:
            print(f"\n{'#' * 40}\nError processing event: {e}\n{'#' * 40}\n")
            # Try to recover the session
            if not self.navigation_handler.handle_session_error(self.web_driver_manager, link, div_id):
                raise e

            # Re-initialize components after recovery
            self._initialize_components()
            return False  # Signal to restart the item loop

        except Exception as e:
            print(f"\n{'#' * 40}\nError processing event: {e}\n{'#' * 40}\n")
            self.navigation_handler.switch_back_to_main_page()
            time.sleep(0.75)
            return True

    def scrape(self, link: str) -> pd.DataFrame:
        """Main scraping method for a given URL with session recovery."""
        df = pd.DataFrame(columns=self.config.DATA_COLUMNS)

        # Load previous progress
        last_processed_id, last_url = ProgressManager.load_progress()

        # Initialize driver and components
        self.web_driver_manager.initialize_driver()
        self._initialize_components()

        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Setup initial navigation
                if not self._setup_initial_navigation(
                    link, last_processed_id if last_url == link else None, retry_count
                ):
                    raise Exception("Failed to setup initial navigation")

                # Get the main container
                main_div = self.web_driver_manager.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
                )

                # Process events in batches
                old_length = 0
                while True:
                    # Check for stop signal
                    if self.keyboard_handler.should_stop():
                        print("🛑 Stop signal received. Saving current progress and exiting...")
                        return df

                    try:
                        child_items = main_div.find_elements(By.XPATH, "./div[@id]")
                        restart_loop = False

                        for item in child_items[old_length:]:
                            # Check for stop signal before processing each item
                            if self.keyboard_handler.should_stop():
                                print("🛑 Stop signal received. Saving current progress and exiting...")
                                return df

                            div_id = int(item.get_attribute("id"))

                            # Process the event and check if we need to restart the loop
                            result = self._process_single_event(item, div_id, link, df, last_processed_id, retry_count)

                            if not result:  # Session error occurred, restart loop
                                main_div = self.web_driver_manager.wait.until(
                                    EC.presence_of_element_located(
                                        (By.CSS_SELECTOR, "div.wrapper.row.event-search-items")
                                    )
                                )
                                restart_loop = True
                                break

                            if len(df.index) >= self.config.DEFAULT_MAX_LIST_SIZE:
                                print("Reached maximum list size. Returning table...")
                                return df

                        if restart_loop:
                            continue

                        if not self.navigation_handler.load_more_events():
                            print("No more items to load.")
                            break

                        old_length = len(child_items)

                    except (WebDriverException, TimeoutException) as e:
                        print(f"\n{'#' * 40}\nError in main loop: {e}\n{'#' * 40}\n")
                        # Try to recover the session
                        if not self.navigation_handler.handle_session_error(self.web_driver_manager, link):
                            raise e

                        # Re-initialize components and re-find the main div after recovery
                        self._initialize_components()
                        main_div = self.web_driver_manager.wait.until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
                        )
                        continue

                # If we reach here, scraping completed successfully
                print("Scraping completed successfully.")
                break

            except Exception as e:
                retry_count += 1
                print(f"\n{'#' * 40}\nError in retry loop: {e}\n{'#' * 40}\n")

                if retry_count < max_retries:
                    print(f"Retrying... Attempt {retry_count + 1}/{max_retries}")
                    time.sleep(5)  # Wait before retry
                else:
                    print("Max retries reached. Returning partial results.")
                    break

        self.web_driver_manager.quit_driver()
        self.keyboard_handler.cleanup()
        print("Returning table...")
        return df

    def save_data(self, df: pd.DataFrame):
        """Save scraped data to CSV file."""
        combined_df = pd.concat([self.existing_data, df], ignore_index=True)
        combined_df.to_csv(self.config.OUTPUT_FILE, index=False, sep=";", encoding="utf-8-sig")

        # Provide detailed save information
        new_records = len(df)
        total_records = len(combined_df)
        print(f"\n💾 DATA SAVED SUCCESSFULLY!")
        print(f"   • New records scraped: {new_records}")
        print(f"   • Total records in file: {total_records}")
        print(f"   • File location: {self.config.OUTPUT_FILE}")

        # Clean up progress file after successful save
        ProgressManager.clear_progress()

    def run(self, url_index: int = 0):
        """Run the scraper for a specific URL index."""
        if url_index >= len(self.config.SCRAPING_URLS):
            raise ValueError(f"URL index {url_index} is out of range")

        url = self.config.SCRAPING_URLS[url_index]
        scraped_df = self.scrape(url)

        # Save data even if stopped early
        if len(scraped_df) > 0:
            self.save_data(scraped_df)
        else:
            print("No new data to save.")

        # Check if stopped early
        if self.keyboard_handler.should_stop():
            print("🏁 Scraping stopped by user request.")


def main():
    """Main execution function."""
    scraper = PassoScraper()
    try:
        scraper.run(url_index=0)  # Run scraper for the first URL
    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received. Saving current progress...")
    except Exception as e:
        print(f"Error during scraping: {e}")
    finally:
        # Ensure driver and keyboard handler are cleaned up
        scraper.web_driver_manager.quit_driver()
        scraper.keyboard_handler.cleanup()
        print("\n✅ Scraper cleanup completed.")


if __name__ == "__main__":
    main()
