#!/usr/bin/env python3
"""
Test script to demonstrate the keyboard shortcut functionality
for stopping the scraper and saving progress.
"""

import time
import threading
from passo import KeyboardInterruptHandler

def simulate_scraping_work():
    """Simulate scraping work with progress updates."""
    print("🚀 Starting simulated scraping...")
    print("📋 KEYBOARD SHORTCUTS:")
    print("   • Type 'q', 'quit', 'stop', or 's' + Enter to stop scraping")
    print("   • Press Ctrl+C to force stop")
    print("-" * 50)
    
    # Create keyboard handler
    keyboard_handler = KeyboardInterruptHandler()
    
    try:
        # Simulate processing items
        for i in range(1, 101):  # Simulate 100 items
            if keyboard_handler.should_stop():
                print(f"\n🛑 Stop signal received at item {i}!")
                print("💾 Saving current progress...")
                print(f"✅ Saved progress for {i-1} items.")
                break
            
            print(f"Processing item {i}/100...", end='\r')
            time.sleep(0.5)  # Simulate work
            
        else:
            print("\n✅ All items processed successfully!")
            
    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received!")
        print("💾 Saving current progress...")
        
    finally:
        keyboard_handler.cleanup()
        print("🧹 Cleanup completed.")

if __name__ == "__main__":
    simulate_scraping_work()
